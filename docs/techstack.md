# Tech Stack

This document outlines the technologies and libraries used in this project.

## Framework

*   **[Next.js](https://nextjs.org/):** A React framework for building full-stack web applications.

## Artificial Intelligence

*   **[Genkit](https://firebase.google.com/docs/genkit):** A framework for building AI-powered features for Node.js and Go applications.
    *   `@genkit-ai/googleai`: Google AI provider for Genkit.
    *   `@genkit-ai/next`: Next.js integration for Genkit.

## UI

*   **[React](https://react.dev/):** A JavaScript library for building user interfaces.
*   **[Shadcn UI](https://ui.shadcn.com/):** A collection of re-usable components built using Radix UI and Tailwind CSS.
*   **[Radix UI](https://www.radix-ui.com/):** A low-level UI component library for building high-quality, accessible design systems and web apps.
*   **[Tailwind CSS](https://tailwindcss.com/):** A utility-first CSS framework for rapidly building custom user interfaces.
*   **[Recharts](https://recharts.org/):** A composable charting library built on React components.
*   **[Lucide React](https://lucide.dev/):** A library of simply beautiful open-source icons.

## Forms

*   **[React Hook Form](https://react-hook-form.com/):** A library for managing forms in React.
*   **[Zod](https://zod.dev/):** A TypeScript-first schema declaration and validation library.

## Language

*   **[TypeScript](https://www.typescriptlang.org/):** A typed superset of JavaScript that compiles to plain JavaScript.

## Other Libraries

*   **[date-fns](https://date-fns.org/):** A modern JavaScript date utility library.
*   **[clsx](https://github.com/lukeed/clsx):** A tiny utility for constructing `className` strings conditionally.
*   **[tailwind-merge](https://github.com/dcastil/tailwind-merge):** A utility for merging Tailwind CSS classes in JavaScript without style conflicts.
