I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The codebase has a solid foundation with working AI extraction flows and server actions, but lacks the HTTP API layer entirely. A critical discrepancy exists between the documentation (which mentions a "merchant" field) and the actual schema (which only defines 5 fields: description, category, type, amount, date). The project needs significant additions: firebase-admin dependency, authentication infrastructure, API routes, structured error handling, and a complete testing setup. The existing `ExtractTransactionDetailsOutput` schema will serve as the source of truth for available fields.

### Approach

I'll implement a comprehensive API layer for the Finance Bot's transaction extraction functionality. The implementation will build upon the existing AI flows and server actions, adding proper HTTP API endpoints with Firebase App Check authentication, structured error handling, and comprehensive testing.

The approach follows a layered architecture:
1. **Infrastructure Layer**: Firebase Admin SDK setup and authentication helpers
2. **API Layer**: Next.js 15 App Router route handlers with proper validation and error handling  
3. **Testing Layer**: Comprehensive test suite covering all endpoints and scenarios
4. **Documentation**: Updated implementation docs reflecting actual available fields

Key design decisions:
- Reuse existing `getTransactionDetails` server action for business logic
- Implement RFC 7807 Problem Details for structured error responses
- Use dynamic routing for field-specific endpoints to reduce code duplication
- Add firebase-admin dependency for server-side App Check verification
- Choose Vitest as testing framework for better ESM support and performance

### Reasoning

I analyzed the provided API implementation document and explored the existing codebase structure. I examined the current AI flows, server actions, and project configuration to understand what's already implemented versus what needs to be created. I investigated the Firebase setup, routing patterns, type definitions, error handling approaches, and testing infrastructure to identify gaps and requirements. I also researched best practices for Next.js 15 App Router API development, Firebase App Check authentication, structured error handling, and testing patterns.

## Mermaid Diagram

sequenceDiagram
    participant Client
    participant API as API Route Handler
    participant Auth as verifyAppCheck()
    participant Validation as Request Validation
    participant Action as getTransactionDetails()
    participant AI as AI Flow
    participant Response as Response Builder

    Client->>API: POST /api/v1/transactions/extract
    Note over Client,API: { text: "...", fields?: [...] }
    
    API->>Auth: Verify App Check Token
    alt Debug Mode Enabled
        Auth-->>API: Skip verification
    else Production Mode
        Auth->>Auth: Check X-Firebase-AppCheck header
        Auth->>Auth: Verify with Firebase Admin SDK
        alt Invalid/Missing Token
            Auth-->>API: Throw AuthError
            API-->>Client: 401 Unauthorized (Problem Details)
        end
    end
    
    API->>Validation: Validate request body
    alt Invalid Request
        Validation-->>API: Throw ValidationError
        API-->>Client: 400 Bad Request (Problem Details)
    end
    
    API->>Action: Call getTransactionDetails(input)
    Action->>AI: extractTransactionDetails()
    AI-->>Action: ExtractTransactionDetailsOutput
    Action-->>API: ActionResult { data: ... }
    
    API->>Response: Process response
    alt Selective Extraction
        Response->>Response: Filter fields
    end
    Response-->>API: Filtered/Full response
    
    API-->>Client: 200 OK (JSON)

## Proposed File Changes

### package.json(MODIFY)

Add the missing dependencies required for the API implementation:

1. **firebase-admin**: Add `"firebase-admin": "^12.0.0"` to dependencies for server-side Firebase App Check token verification

2. **Testing dependencies**: Add to devDependencies:
   - `"vitest": "^1.0.0"` - Modern test runner with excellent ESM support
   - `"@vitest/ui": "^1.0.0"` - Optional UI for test visualization
   - `"next-test-api-route-handler": "^4.0.0"` - Helper for testing Next.js App Router API routes

3. **Scripts**: Add test-related scripts:
   - `"test": "vitest"` - Run tests
   - `"test:ui": "vitest --ui"` - Run tests with UI
   - `"test:coverage": "vitest --coverage"` - Run tests with coverage

These additions enable Firebase App Check verification on the server side and provide a complete testing infrastructure for the new API endpoints.

### .env.example(NEW)

References: 

- docs/api-impl.md(MODIFY)

Create an environment variables template file documenting all required environment variables for the API:

1. **Authentication Control**:
   - `API_DEBUG_MODE_ENABLED=true` - Set to true to bypass Firebase App Check authentication during development

2. **Firebase Admin SDK Configuration** (choose one approach):
   
   **Option A - Service Account File Path**:
   - `GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json`
   - `FIREBASE_PROJECT_ID=your-firebase-project-id`
   
   **Option B - Environment Variables**:
   - `FIREBASE_PROJECT_ID=your-firebase-project-id`
   - `FIREBASE_CLIENT_EMAIL=<EMAIL>`
   - `FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"`

3. **Development Notes**:
   - Include comments explaining when to use each approach
   - Note that FIREBASE_PRIVATE_KEY must have literal \n characters replaced with actual newlines
   - Explain that Option A is simpler for local development, Option B is better for serverless deployment

This file serves as documentation for developers setting up the project locally and for deployment configuration.

### src/lib/firebaseAdmin.ts(NEW)

References: 

- docs/api-impl.md(MODIFY)

Create a Firebase Admin SDK initialization module that provides a singleton Admin app instance:

1. **Import Dependencies**: Import firebase-admin and credential helpers

2. **Initialization Logic**:
   - Check if Admin app is already initialized to prevent duplicate initialization
   - Support multiple credential approaches:
     - Application Default Credentials (for GCP deployment)
     - Service account file via GOOGLE_APPLICATION_CREDENTIALS
     - Service account object from environment variables
   - Use FIREBASE_PROJECT_ID environment variable for project configuration

3. **Error Handling**: Throw descriptive errors if required environment variables are missing or if initialization fails

4. **Export**: Export the initialized admin app instance for use in authentication helpers

5. **Type Safety**: Ensure proper TypeScript types for all Firebase Admin SDK interactions

This module centralizes Firebase Admin SDK setup and handles the complexity of different credential approaches, making it easy to use across the application.

### src/lib/auth(NEW)

Create the auth directory to contain authentication-related utilities.

### src/lib/auth/verifyAppCheck.ts(NEW)

References: 

- docs/api-impl.md(MODIFY)
- src/lib/firebaseAdmin.ts(NEW)

Create the Firebase App Check token verification helper that implements the authentication logic specified in `docs/api-impl.md`:

1. **Environment Check**: Read `API_DEBUG_MODE_ENABLED` environment variable and bypass all authentication if set to 'true'

2. **Token Extraction**: Extract the App Check token from the `X-Firebase-AppCheck` header in the request

3. **Token Verification**: Use the Firebase Admin SDK from `src/lib/firebaseAdmin.ts` to verify the token:
   - Call `admin.appCheck().verifyToken(token)`
   - Handle verification success and failure cases

4. **Error Handling**: Define custom error types for different authentication failure scenarios:
   - `AppCheckMissingError` - No token provided
   - `AppCheckInvalidError` - Token verification failed
   - Include error details for logging while keeping client responses secure

5. **Function Signature**: Accept a NextRequest object and return a Promise that resolves on success or throws typed errors on failure

6. **Logging**: Log authentication attempts and failures for security monitoring

This helper encapsulates all App Check verification logic and can be easily mocked in tests.

### src/lib/errors.ts(NEW)

References: 

- src/lib/auth/verifyAppCheck.ts(NEW)
- src/app/actions.ts

Create a structured error handling system implementing RFC 7807 Problem Details for HTTP APIs:

1. **Error Types**: Define TypeScript interfaces for structured errors:
   - `ProblemDetails` interface with type, title, status, detail, instance fields
   - `ValidationError` interface for field-level validation errors
   - Custom error classes extending Error for different scenarios

2. **Error Mapping Functions**:
   - `mapAuthErrorToResponse()` - Convert App Check errors to 401/403 responses
   - `mapValidationErrorToResponse()` - Convert Zod validation errors to 400 responses with field details
   - `mapGenericErrorToResponse()` - Convert unexpected errors to 500 responses

3. **Response Helpers**:
   - `createProblemResponse()` - Create Response objects with proper Content-Type: application/problem+json
   - `createValidationErrorResponse()` - Specialized helper for validation error responses

4. **Zod Integration**: Helper to convert Zod error issues array into structured validation error format with field paths and messages

5. **Logging Integration**: Ensure all error mapping functions log appropriate details server-side while returning sanitized responses to clients

This module provides consistent, standards-compliant error responses across all API endpoints.

### src/lib/validation.ts(NEW)

References: 

- src/ai/flows/extract-transaction-details.ts
- src/lib/errors.ts(NEW)

Create API-specific validation schemas and utilities:

1. **Request Schemas**: Define Zod schemas for API request validation:
   - `ExtractRequestSchema` - Validates { text: string, fields?: string[] }
   - `FieldSpecificRequestSchema` - Validates { text: string } for field-specific endpoints

2. **Field Validation**: Create utilities for validating the `fields` parameter:
   - `VALID_FIELDS` constant array containing the 5 available fields from `ExtractTransactionDetailsOutputSchema`: ['description', 'category', 'type', 'amount', 'date']
   - `validateFields()` function to check that requested fields are valid
   - `filterResponseByFields()` function to filter response objects to include only requested fields

3. **Request Body Parsing**: Helper functions to safely parse and validate JSON request bodies with proper error handling

4. **Type Guards**: TypeScript type guards for runtime type checking of request data

5. **Integration**: Ensure compatibility with existing types from `src/ai/flows/extract-transaction-details.ts`

This module centralizes all API validation logic and ensures consistency across endpoints.

### src/app/api(NEW)

Create the api directory to contain all API route handlers.

### src/app/api/v1(NEW)

Create the v1 directory for versioned API endpoints.

### src/app/api/v1/transactions(NEW)

Create the transactions directory for transaction-related API endpoints.

### src/app/api/v1/transactions/extract(NEW)

Create the extract directory for the main extraction endpoint.

### src/app/api/v1/transactions/extract/route.ts(NEW)

References: 

- docs/api-impl.md(MODIFY)
- src/app/actions.ts
- src/lib/auth/verifyAppCheck.ts(NEW)
- src/lib/validation.ts(NEW)
- src/lib/errors.ts(NEW)

Implement the main extraction API endpoint that handles both all-in-one and selective extraction as specified in `docs/api-impl.md`:

1. **POST Handler**: Export async POST function that accepts NextRequest and returns Response

2. **Authentication**: Use `verifyAppCheck()` from `src/lib/auth/verifyAppCheck.ts` to authenticate requests

3. **Request Validation**: 
   - Parse JSON body using validation utilities from `src/lib/validation.ts`
   - Validate required `text` field and optional `fields` array
   - Return 400 Bad Request for validation failures using error utilities from `src/lib/errors.ts`

4. **Business Logic**: 
   - Call `getTransactionDetails()` from `src/app/actions.ts` with the validated input
   - Handle the ActionResult response (success/error cases)

5. **Response Processing**:
   - For requests without `fields`: return full `ExtractTransactionDetailsOutput`
   - For requests with `fields`: filter response using `filterResponseByFields()` to include only requested fields
   - Return 200 OK with JSON response

6. **Error Handling**: Use error mapping functions from `src/lib/errors.ts` to convert various error types to appropriate HTTP responses (401, 400, 413, 500)

7. **Content-Type**: Set proper Content-Type headers for both success (application/json) and error (application/problem+json) responses

This endpoint serves as the foundation for both all-in-one and selective extraction functionality.

### src/app/api/v1/transactions/extract/[field](NEW)

Create the dynamic field directory for field-specific endpoints.

### src/app/api/v1/transactions/extract/[field]/route.ts(NEW)

References: 

- docs/api-impl.md(MODIFY)
- src/app/api/v1/transactions/extract/route.ts(NEW)
- src/lib/validation.ts(NEW)

Implement field-specific extraction endpoints using Next.js dynamic routing:

1. **Dynamic Route Parameter**: Extract the `field` parameter from the route params to determine which field to extract

2. **Field Validation**: 
   - Validate that the requested field is one of the 5 valid fields: description, category, type, amount, date
   - Return 404 Not Found for invalid field names
   - Use the `VALID_FIELDS` constant from `src/lib/validation.ts`

3. **POST Handler**: 
   - Accept same authentication and request validation as the main endpoint
   - Parse and validate request body (should contain only `text` field)

4. **Business Logic**: 
   - Call the main extraction logic by internally forwarding to the selective extraction functionality
   - Set `fields: [field]` to extract only the requested field

5. **Response**: 
   - Return a JSON object containing only the requested field
   - Example: for `/extract/amount`, return `{ "amount": 25.99 }`
   - Maintain consistent error handling with the main endpoint

6. **Code Reuse**: Leverage the validation, authentication, and error handling utilities from the main endpoint to avoid duplication

This dynamic route handles all field-specific endpoints (/description, /category, /type, /amount, /date) with a single implementation, reducing code duplication while maintaining the API contract specified in `docs/api-impl.md`.

### vitest.config.ts(NEW)

References: 

- tsconfig.json

Create Vitest configuration for testing the API endpoints:

1. **Basic Configuration**: 
   - Set test environment to 'node' for server-side API testing
   - Configure TypeScript support with proper path resolution
   - Set up path aliases to match tsconfig.json (@/* -> ./src/*)

2. **Test File Patterns**: 
   - Include test files with patterns: **/*.{test,spec}.{js,ts}
   - Configure test directory structure

3. **Coverage Configuration**: 
   - Set up coverage reporting for src/ directory
   - Exclude test files and configuration files from coverage
   - Configure coverage thresholds if desired

4. **Environment Variables**: 
   - Set up test environment variables (API_DEBUG_MODE_ENABLED=true for easier testing)
   - Configure any required test-specific environment setup

5. **Mocking Setup**: 
   - Configure global mocks for Firebase Admin SDK if needed
   - Set up any other global test utilities

This configuration enables comprehensive testing of the API endpoints with proper TypeScript support and path resolution.

### tests(NEW)

Create the tests directory to contain all test files.

### tests/api(NEW)

Create the api directory to mirror the API route structure in tests.

### tests/api/v1(NEW)

Create the v1 directory for versioned API tests.

### tests/api/v1/transactions(NEW)

Create the transactions directory for transaction API tests.

### tests/api/v1/transactions/extract(NEW)

Create the extract directory for extraction endpoint tests.

### tests/api/v1/transactions/extract/route.test.ts(NEW)

References: 

- docs/api-impl.md(MODIFY)
- src/app/api/v1/transactions/extract/route.ts(NEW)
- src/app/actions.ts

Create comprehensive tests for the main extraction endpoint covering all scenarios from `docs/api-impl.md`:

1. **Test Setup**: 
   - Import next-test-api-route-handler for App Router testing
   - Mock Firebase Admin SDK verification functions
   - Mock the `getTransactionDetails` action from `src/app/actions.ts`
   - Set up test data and helper functions

2. **Happy Path Tests**:
   - Test successful all-in-one extraction (no fields parameter)
   - Test successful selective extraction with valid fields array
   - Verify correct response format and status codes (200 OK)
   - Test with API_DEBUG_MODE_ENABLED=true (auth bypass)

3. **Authentication Error Tests**:
   - Test missing X-Firebase-AppCheck header → 401 Unauthorized
   - Test invalid App Check token → 401 Unauthorized
   - Test expired App Check token → 401 Unauthorized
   - Verify error response format follows RFC 7807 Problem Details

4. **Input Validation Error Tests**:
   - Test missing `text` field → 400 Bad Request
   - Test empty `text` field → 400 Bad Request
   - Test invalid `fields` array (non-existent field names) → 400 Bad Request
   - Test oversized text input → 413 Payload Too Large or 400 Bad Request

5. **Selective Extraction Tests**:
   - Test with single field in fields array
   - Test with multiple fields in fields array
   - Verify response contains only requested fields
   - Test with all valid fields

6. **Error Handling Tests**:
   - Test server action returning error → appropriate error response
   - Test unexpected server errors → 500 Internal Server Error

Each test should verify both the HTTP status code and the response body structure.

### tests/api/v1/transactions/extract/field-specific.test.ts(NEW)

References: 

- docs/api-impl.md(MODIFY)
- src/app/api/v1/transactions/extract/[field]/route.ts(NEW)
- src/ai/flows/extract-transaction-details.ts

Create tests for field-specific endpoints covering all 5 available fields:

1. **Test Setup**: 
   - Import and configure next-test-api-route-handler for dynamic route testing
   - Mock Firebase Admin SDK and server actions
   - Set up test data for each field type

2. **Valid Field Tests**: Create tests for each of the 5 valid fields:
   - `/extract/description` → returns { "description": "..." }
   - `/extract/category` → returns { "category": "..." }
   - `/extract/type` → returns { "type": "..." }
   - `/extract/amount` → returns { "amount": 123.45 }
   - `/extract/date` → returns { "date": "2024-01-01" }

3. **Invalid Field Tests**:
   - Test `/extract/merchant` → 404 Not Found (field doesn't exist in schema)
   - Test `/extract/invalid` → 404 Not Found
   - Test other non-existent field names

4. **Authentication and Validation Tests**:
   - Verify same authentication requirements as main endpoint
   - Test missing/invalid text field → 400 Bad Request
   - Test authentication failures → 401 Unauthorized

5. **Response Format Tests**:
   - Verify each endpoint returns only the requested field
   - Verify proper JSON structure and content types
   - Test null values for nullable fields (category, amount, date)

6. **Integration Tests**:
   - Test that field-specific endpoints produce same results as selective extraction
   - Verify consistency between `/extract?fields=["amount"]` and `/extract/amount`

These tests ensure all field-specific endpoints work correctly and handle edge cases properly.

### tests/lib(NEW)

Create the lib directory for testing utility modules.

### tests/lib/auth.test.ts(NEW)

References: 

- src/lib/auth/verifyAppCheck.ts(NEW)
- src/lib/firebaseAdmin.ts(NEW)

Create unit tests for the authentication utilities:

1. **verifyAppCheck Function Tests**:
   - Test successful token verification
   - Test debug mode bypass (API_DEBUG_MODE_ENABLED=true)
   - Test missing X-Firebase-AppCheck header
   - Test invalid token format
   - Test Firebase Admin SDK verification failures
   - Test different error scenarios and proper error type throwing

2. **Mock Setup**:
   - Mock Firebase Admin SDK `admin.appCheck().verifyToken()` method
   - Mock environment variables for different test scenarios
   - Create helper functions to generate mock NextRequest objects with various headers

3. **Error Handling Tests**:
   - Verify custom error types are thrown correctly
   - Test error message content and structure
   - Ensure sensitive information is not exposed in error messages

4. **Environment Variable Tests**:
   - Test behavior with API_DEBUG_MODE_ENABLED set to various values
   - Test missing environment variables
   - Test Firebase project configuration

These tests ensure the authentication layer works correctly and handles all edge cases securely.

### tests/lib/validation.test.ts(NEW)

References: 

- src/lib/validation.ts(NEW)
- src/ai/flows/extract-transaction-details.ts

Create unit tests for validation utilities:

1. **Field Validation Tests**:
   - Test `validateFields()` with valid field names
   - Test with invalid field names
   - Test with empty arrays and mixed valid/invalid fields
   - Test the `VALID_FIELDS` constant contains exactly the 5 expected fields

2. **Request Schema Tests**:
   - Test `ExtractRequestSchema` with valid requests
   - Test with missing required fields
   - Test with invalid field types
   - Test `FieldSpecificRequestSchema` validation

3. **Response Filtering Tests**:
   - Test `filterResponseByFields()` with various field combinations
   - Test filtering with single fields
   - Test filtering with all fields
   - Test filtering with non-existent fields
   - Verify original object is not modified

4. **Request Body Parsing Tests**:
   - Test successful JSON parsing
   - Test malformed JSON handling
   - Test empty request bodies
   - Test oversized request bodies

5. **Integration Tests**:
   - Test validation utilities work correctly with actual `ExtractTransactionDetailsOutput` objects
   - Verify compatibility with existing types from `src/ai/flows/extract-transaction-details.ts`

These tests ensure all validation logic works correctly and handles edge cases appropriately.

### tests/lib/errors.test.ts(NEW)

References: 

- src/lib/errors.ts(NEW)
- src/lib/auth/verifyAppCheck.ts(NEW)

Create unit tests for error handling utilities:

1. **Error Mapping Tests**:
   - Test `mapAuthErrorToResponse()` with different authentication error types
   - Test `mapValidationErrorToResponse()` with Zod validation errors
   - Test `mapGenericErrorToResponse()` with unexpected errors
   - Verify correct HTTP status codes are returned

2. **Problem Details Format Tests**:
   - Test `createProblemResponse()` generates RFC 7807 compliant responses
   - Verify Content-Type headers are set correctly
   - Test all required Problem Details fields are present
   - Test optional fields and extensions

3. **Validation Error Conversion Tests**:
   - Test conversion of Zod error issues to structured validation errors
   - Test field path extraction and formatting
   - Test error message sanitization
   - Test multiple validation errors in single response

4. **Response Structure Tests**:
   - Verify error responses have consistent structure
   - Test that sensitive information is not leaked in error responses
   - Test error response serialization

5. **Logging Integration Tests**:
   - Verify errors are logged appropriately server-side
   - Test log message format and content
   - Ensure client responses don't contain sensitive log data

These tests ensure error handling is consistent, secure, and follows established standards.

### docs/api-impl.md(MODIFY)

References: 

- src/ai/flows/extract-transaction-details.ts

Update the API implementation document to reflect the actual available fields and correct the discrepancy found during implementation:

1. **Field Corrections**: 
   - Remove references to "merchant" field as it doesn't exist in the `ExtractTransactionDetailsOutputSchema`
   - Update all examples to use only the 5 actual available fields: description, category, type, amount, date
   - Correct the field-specific endpoints section to list the actual available endpoints

2. **Available Field-Specific Endpoints**:
   - `/api/v1/transactions/extract/description`
   - `/api/v1/transactions/extract/category`
   - `/api/v1/transactions/extract/type`
   - `/api/v1/transactions/extract/amount`
   - `/api/v1/transactions/extract/date`

3. **Example Updates**:
   - Update selective extraction examples to use valid field names
   - Update request/response examples throughout the document
   - Ensure all code snippets reference actual schema fields

4. **Implementation Notes**:
   - Add note about using dynamic routing for field-specific endpoints
   - Document the use of RFC 7807 Problem Details for error responses
   - Add information about the testing infrastructure

5. **Dependencies Section**:
   - Add note about firebase-admin dependency requirement
   - Document environment variable requirements

This update ensures the documentation accurately reflects the implementation and prevents confusion about available fields.